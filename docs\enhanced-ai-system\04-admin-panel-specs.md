# Enhanced Admin Panel Specifications

## Overview

This document defines the comprehensive specifications for the enhanced admin panel that will provide complete control over the AI-powered content generation system, including job monitoring, bulk processing, editorial workflow, and system configuration.

## Admin Panel Architecture

### 1. Layout Structure
```typescript
interface AdminLayoutStructure {
  header: {
    logo: string;
    userMenu: AdminUserMenu;
    notifications: NotificationCenter;
    systemStatus: SystemStatusIndicator;
  };
  sidebar: {
    navigation: AdminNavigation;
    quickActions: QuickActionPanel;
    systemMetrics: LiveMetrics;
  };
  mainContent: {
    breadcrumbs: BreadcrumbNavigation;
    pageContent: DynamicContent;
    actionBar: ContextualActions;
  };
  footer: {
    systemInfo: SystemInformation;
    supportLinks: SupportLinks;
  };
}
```

### 2. Navigation Structure
```typescript
const ADMIN_NAVIGATION = {
  dashboard: {
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    path: '/admin',
    children: []
  },
  tools: {
    label: 'Tool Management',
    icon: 'Wrench',
    path: '/admin/tools',
    children: [
      { label: 'All Tools', path: '/admin/tools' },
      { label: 'Add New Tool', path: '/admin/tools/new' },
      { label: 'Bulk Import', path: '/admin/tools/bulk' },
      { label: 'Draft Tools', path: '/admin/tools/drafts' },
      { label: 'Published Tools', path: '/admin/tools/published' }
    ]
  },
  jobs: {
    label: 'Job Monitoring',
    icon: 'Activity',
    path: '/admin/jobs',
    children: [
      { label: 'Active Jobs', path: '/admin/jobs/active' },
      { label: 'Job History', path: '/admin/jobs/history' },
      { label: 'Failed Jobs', path: '/admin/jobs/failed' },
      { label: 'Bulk Operations', path: '/admin/jobs/bulk' }
    ]
  },
  content: {
    label: 'Content Generation',
    icon: 'Bot',
    path: '/admin/content',
    children: [
      { label: 'AI Configuration', path: '/admin/content/ai-config' },
      { label: 'Prompt Management', path: '/admin/content/prompts' },
      { label: 'Generation Queue', path: '/admin/content/queue' },
      { label: 'Content Review', path: '/admin/content/review' }
    ]
  },
  editorial: {
    label: 'Editorial Control',
    icon: 'Edit',
    path: '/admin/editorial',
    children: [
      { label: 'Review Queue', path: '/admin/editorial/review' },
      { label: 'Featured Tools', path: '/admin/editorial/featured' },
      { label: 'Editorial Calendar', path: '/admin/editorial/calendar' },
      { label: 'Content Standards', path: '/admin/editorial/standards' }
    ]
  },
  analytics: {
    label: 'Analytics',
    icon: 'BarChart3',
    path: '/admin/analytics',
    children: [
      { label: 'Performance Metrics', path: '/admin/analytics/performance' },
      { label: 'Content Quality', path: '/admin/analytics/quality' },
      { label: 'System Usage', path: '/admin/analytics/usage' },
      { label: 'Cost Analysis', path: '/admin/analytics/costs' }
    ]
  },
  settings: {
    label: 'System Settings',
    icon: 'Settings',
    path: '/admin/settings',
    children: [
      { label: 'API Configuration', path: '/admin/settings/api' },
      { label: 'System Configuration', path: '/admin/settings/system' },
      { label: 'User Management', path: '/admin/settings/users' },
      { label: 'Backup & Recovery', path: '/admin/settings/backup' }
    ]
  }
};
```

## Core Admin Features

### 1. Job Monitoring Dashboard
```typescript
interface JobMonitoringDashboard {
  realTimeMetrics: {
    activeJobs: number;
    queuedJobs: number;
    completedToday: number;
    failureRate: number;
  };
  jobList: {
    columns: ['id', 'type', 'status', 'progress', 'created', 'actions'];
    filters: ['status', 'type', 'dateRange', 'toolId'];
    sorting: ['created_at', 'updated_at', 'status', 'progress'];
    pagination: { pageSize: 25, totalPages: number };
  };
  jobDetails: {
    basicInfo: JobBasicInfo;
    progressTracking: ProgressTracker;
    logs: JobLogs;
    scrapedData: ScrapedDataViewer;
    aiResponses: AIResponseViewer;
    errorDetails: ErrorDetailsViewer;
  };
  jobControls: {
    pause: (jobId: string) => Promise<void>;
    resume: (jobId: string) => Promise<void>;
    stop: (jobId: string) => Promise<void>;
    retry: (jobId: string) => Promise<void>;
    delete: (jobId: string) => Promise<void>;
  };
}
```

### 2. Bulk Processing Interface
```typescript
interface BulkProcessingInterface {
  uploadMethods: {
    textFile: {
      accept: '.txt';
      description: 'Plain text file with one URL per line';
      maxSize: '10MB';
      processor: (file: File) => Promise<string[]>;
    };
    jsonFile: {
      accept: '.json';
      description: 'JSON file with tool data and URL mapping';
      maxSize: '50MB';
      processor: (file: File) => Promise<BulkToolData[]>;
    };
    manualEntry: {
      interface: 'textarea';
      placeholder: 'Enter URLs, one per line';
      validation: (urls: string[]) => ValidationResult;
    };
  };
  processingOptions: {
    scrapeOnly: boolean;
    generateContent: boolean;
    autoPublish: boolean;
    priority: 'low' | 'normal' | 'high';
    batchSize: number;
    delayBetweenRequests: number;
  };
  progressTracking: {
    totalItems: number;
    processed: number;
    successful: number;
    failed: number;
    estimatedTimeRemaining: string;
    currentItem: string;
  };
  resultsSummary: {
    successfulTools: ToolSummary[];
    failedTools: FailedToolSummary[];
    warnings: Warning[];
    downloadResults: () => void;
  };
}
```

### 3. Content Generation Controls
```typescript
interface ContentGenerationControls {
  aiConfiguration: {
    providers: {
      openai: {
        enabled: boolean;
        apiKey: string;
        model: 'gpt-4o' | 'gpt-4-turbo';
        maxTokens: number;
        temperature: number;
      };
      openrouter: {
        enabled: boolean;
        apiKey: string;
        model: 'google/gemini-2.0-flash-exp:free';
        maxTokens: number;
        temperature: number;
        promptCaching: boolean;
      };
    };
    modelSelection: {
      strategy: 'auto' | 'manual';
      fallbackOrder: string[];
      costOptimization: boolean;
    };
  };
  promptManagement: {
    systemPrompts: {
      toolAnalysis: PromptTemplate;
      contentCompletion: PromptTemplate;
      contentValidation: PromptTemplate;
    };
    userPrompts: {
      templates: PromptTemplate[];
      customPrompts: CustomPrompt[];
      testValidation: (prompt: string) => Promise<ValidationResult>;
    };
    promptTesting: {
      testPrompt: (prompt: string, sampleData: string) => Promise<TestResult>;
      comparePrompts: (prompts: string[], sampleData: string) => Promise<ComparisonResult>;
    };
  };
  contentValidation: {
    rules: ValidationRule[];
    qualityThresholds: QualityThresholds;
    autoApproval: AutoApprovalSettings;
    manualReview: ManualReviewSettings;
  };
}
```

### 4. Editorial Workflow Management
```typescript
interface EditorialWorkflowManagement {
  reviewQueue: {
    pendingReview: ToolReviewItem[];
    inReview: ToolReviewItem[];
    approved: ToolReviewItem[];
    rejected: ToolReviewItem[];
    filters: ['status', 'reviewer', 'dateRange', 'category'];
  };
  editorialReview: {
    toolPreview: ToolPreviewComponent;
    contentEditor: ContentEditorComponent;
    reviewForm: {
      reviewNotes: string;
      featuredDate: Date;
      editorialText: string; // "was manually vetted by our editorial team and was first featured on [date]"
      approvalStatus: 'approved' | 'rejected' | 'needs_revision';
      reviewer: string;
    };
    actions: {
      approve: (toolId: string, reviewData: ReviewData) => Promise<void>;
      reject: (toolId: string, reason: string) => Promise<void>;
      requestRevision: (toolId: string, notes: string) => Promise<void>;
      setFeatured: (toolId: string, featuredDate: Date) => Promise<void>;
    };
  };
  featuredTools: {
    currentFeatured: FeaturedTool[];
    scheduledFeatured: ScheduledFeaturedTool[];
    featuredHistory: FeaturedToolHistory[];
    manageFeatured: {
      addToFeatured: (toolId: string, date: Date) => Promise<void>;
      removeFromFeatured: (toolId: string) => Promise<void>;
      updateFeaturedDate: (toolId: string, newDate: Date) => Promise<void>;
    };
  };
  contentStandards: {
    guidelines: ContentGuidelines;
    qualityChecklist: QualityChecklist;
    styleGuide: StyleGuide;
    approvalCriteria: ApprovalCriteria;
  };
}
```

## UI/UX Specifications

### 1. Design System Integration
```typescript
const ADMIN_DESIGN_SYSTEM = {
  colors: {
    primary: 'rgb(255, 150, 0)', // Custom orange
    primaryHover: 'rgb(255, 170, 30)',
    background: '#18181b', // zinc-900
    surface: '#27272a', // zinc-800
    border: '#000000', // Black borders
    text: {
      primary: '#ffffff',
      secondary: '#a1a1aa',
      muted: '#71717a'
    },
    status: {
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    }
  },
  typography: {
    fontFamily: 'Roboto, sans-serif',
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem'
    }
  },
  spacing: {
    containerPadding: '1rem',
    sectionGap: '2rem',
    cardPadding: '1.5rem',
    buttonPadding: '0.75rem 1.5rem'
  },
  shadows: {
    card: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    cardHover: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    modal: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
  }
};
```

### 2. Component Specifications
```typescript
interface AdminComponentSpecs {
  dataTable: {
    features: ['sorting', 'filtering', 'pagination', 'selection', 'export'];
    styling: {
      headerBackground: 'bg-zinc-800',
      rowHover: 'hover:bg-zinc-800',
      borders: 'border-black',
      text: 'text-white'
    };
    interactions: {
      rowClick: 'navigate to detail view',
      headerClick: 'sort by column',
      checkboxSelect: 'multi-select for bulk actions'
    };
  };
  forms: {
    validation: 'react-hook-form + zod',
    styling: {
      inputs: 'bg-zinc-800 border-black text-white',
      labels: 'text-white font-medium',
      errors: 'text-red-400',
      buttons: 'bg-orange-500 hover:bg-orange-600'
    };
    features: ['real-time validation', 'auto-save drafts', 'progress indicators'];
  };
  modals: {
    backdrop: 'bg-black/50',
    container: 'bg-zinc-900 border border-black',
    animations: 'fade-in/scale-up',
    sizes: ['sm', 'md', 'lg', 'xl', 'full'];
  };
  notifications: {
    types: ['success', 'error', 'warning', 'info'],
    positioning: 'top-right',
    duration: 'auto-dismiss after 5s',
    actions: ['dismiss', 'undo', 'view details'];
  };
}
```

### 3. Responsive Design Requirements
```typescript
const RESPONSIVE_BREAKPOINTS = {
  mobile: {
    maxWidth: '768px',
    layout: 'single-column',
    navigation: 'bottom-tabs',
    sidebar: 'hidden',
    tables: 'card-view'
  },
  tablet: {
    minWidth: '769px',
    maxWidth: '1024px',
    layout: 'collapsible-sidebar',
    navigation: 'top-header',
    sidebar: 'overlay',
    tables: 'horizontal-scroll'
  },
  desktop: {
    minWidth: '1025px',
    layout: 'full-sidebar',
    navigation: 'persistent-sidebar',
    sidebar: 'always-visible',
    tables: 'full-featured'
  }
};
```

## Data Inspection Tools

### 1. Scraped Data Viewer
```typescript
interface ScrapedDataViewer {
  rawData: {
    markdown: string;
    metadata: ScrapedMetadata;
    timestamp: string;
    source: string;
  };
  processedData: {
    optimizedMarkdown: string;
    extractedImages: MediaAsset[];
    extractedFavicon: FaviconData;
    contentSections: ContentSection[];
  };
  viewer: {
    tabs: ['raw', 'processed', 'images', 'metadata'];
    formatting: 'syntax-highlighted markdown';
    search: 'full-text search within content';
    export: 'download as .md file';
  };
  validation: {
    contentQuality: QualityScore;
    completeness: CompletenessScore;
    issues: ValidationIssue[];
    suggestions: ImprovementSuggestion[];
  };
}
```

### 2. AI Response Viewer
```typescript
interface AIResponseViewer {
  requestData: {
    prompts: PromptData[];
    modelUsed: string;
    tokenUsage: TokenUsage;
    timestamp: string;
  };
  responseData: {
    generatedContent: GeneratedContent;
    rawResponse: string;
    processingTime: number;
    confidence: number;
  };
  analysis: {
    contentQuality: QualityAnalysis;
    schemaCompliance: SchemaValidation;
    factualAccuracy: AccuracyCheck;
    styleConsistency: StyleAnalysis;
  };
  actions: {
    regenerate: (withSamePrompt: boolean) => Promise<void>;
    editContent: () => void;
    approveContent: () => Promise<void>;
    rejectContent: (reason: string) => Promise<void>;
  };
}
```

## System Configuration

### 1. API Configuration Interface
```typescript
interface APIConfigurationInterface {
  scrapeDoConfig: {
    apiKey: string;
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    costOptimization: {
      singlePageDefault: boolean;
      batchSize: number;
      delayBetweenRequests: number;
    };
  };
  openaiConfig: {
    apiKey: string;
    organization: string;
    model: string;
    maxTokens: number;
    temperature: number;
    rateLimits: RateLimitConfig;
  };
  openrouterConfig: {
    apiKey: string;
    model: string;
    maxTokens: number;
    temperature: number;
    promptCaching: boolean;
    costTracking: boolean;
  };
  systemConfig: {
    jobProcessing: {
      maxConcurrentJobs: number;
      jobTimeout: number;
      retryAttempts: number;
      cleanupInterval: number;
    };
    contentGeneration: {
      autoApproval: boolean;
      qualityThreshold: number;
      editorialReviewRequired: boolean;
    };
  };
}
```

### 2. User Management Interface
```typescript
interface UserManagementInterface {
  adminUsers: {
    list: AdminUser[];
    roles: ['super_admin', 'admin', 'editor', 'viewer'];
    permissions: Permission[];
    actions: {
      createUser: (userData: CreateUserData) => Promise<AdminUser>;
      updateUser: (userId: string, updates: UserUpdates) => Promise<AdminUser>;
      deleteUser: (userId: string) => Promise<void>;
      resetPassword: (userId: string) => Promise<void>;
      updatePermissions: (userId: string, permissions: Permission[]) => Promise<void>;
    };
  };
  auditLog: {
    entries: AuditLogEntry[];
    filters: ['user', 'action', 'dateRange', 'resource'];
    export: () => Promise<void>;
  };
  sessionManagement: {
    activeSessions: ActiveSession[];
    sessionTimeout: number;
    forceLogout: (sessionId: string) => Promise<void>;
    revokeAllSessions: (userId: string) => Promise<void>;
  };
}
```

---

*This admin panel specification provides comprehensive requirements for implementing a powerful, user-friendly administrative interface that gives complete control over the enhanced AI-powered content generation system while maintaining consistency with the project's design system and user experience standards.*
