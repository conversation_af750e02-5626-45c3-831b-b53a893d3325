Create a comprehensive documentation plan for implementing an enhanced AI-powered content generation and web scraping system for the AI Dude Directory project. This system will replace the current background job and web scraping implementation with a more sophisticated approach.

**Core System Requirements:**

1. **Enhanced Web Scraping & Media Collection:**
   - Implement automatic OG image extraction (Open Graph, Twitter Card, Facebook OG) with fallback to Scrape.do screenshot capture
   - Extract and download favicons from metadata
   - Save all scraped content in organized .md format for reference and reuse
   - Support multi-page scraping (FAQ, pricing, features pages) with cost-optimized single-page default using scrape.do
   - Store scraped data persistently for future AI content generation iterations

2. **Advanced AI Integration:**
   - Integrate both OpenAI and OpenRouter APIs with model selection options
   - Implement intelligent content generation using scraped .md files as context
   - Handle large content by splitting into multiple user prompts with completion waiting
   - Support partial scrape recovery and failure handling mechanisms

3. **Editorial Control Features:**
   - Add admin-configurable editorial review dates (e.g., "manually vetted by our editorial team and first featured on [date]") - this should NOT be AI-generated
   - Implement manual editorial approval workflow for generated content

4. **Bulk Processing System:**
   - Create UI for bulk URL processing from multiple sources:
     - Plain text file uploads (.txt with URLs)
     - JSON file uploads with partial data mapping
     - Manual URL entry with batch processing
   - Implement progress tracking and status monitoring for bulk operations

5. **Admin Panel Enhancements:**
   - Build comprehensive job monitoring dashboard with:
     - Real-time progress tracking
     - Detailed logs and debug information
     - Scraped data preview and validation
     - Pause/resume/stop controls for individual jobs
     - Historical job data and performance metrics
   - Create in-panel content editing system with:
     - Custom user prompt configuration
     - System prompt template management
     - Test validation and format verification
     - Preview and approval workflow

6. **Configuration Management:**
   - Implement dual configuration system (environment variables + admin panel)
   - Store Scrape.do API configuration (API key: 8e7e405ff81145c4afe447610ddb9a7f785f494dddc)
   - Reference Scrape.do documentation: https://scrape.do/documentation/

7. **User Submission Workflow:**
   - Maintain simple URL submission for basic user submissions
   - Provide full manual submission option for complete tool data
   - Implement admin-triggered "scrape and generate" feature for approved simple submissions
   - Separate user-submitted tools from admin-managed tools in the workflow

**Documentation Deliverables Required:**
- System architecture document
- API integration specifications
- Admin panel UI/UX wireframes
- Job processing workflow diagrams
- Data storage and organization schema
- Error handling and recovery procedures
- Configuration management guide
- User submission workflow documentation

**Reference Materials:**
- Review existing discussion in `docs/chat.txt` for context and requirements and issues to be addressed
- Analyze current `docs/project-tasks.md` for integration points
- Consider existing background job system for migration strategy

Create comprehensive documentation for implementing an enhanced AI-powered content generation system to replace the current background job and web scraping approach. This system should integrate with scrape.do API and support multiple AI providers (OpenAI and OpenRouter) with advanced features for bulk processing and admin management.

## Core Requirements

### 1. Enhanced Web Scraping & Data Collection
- **Screenshot Capture**: Implement automatic screenshot capture from Open Graph images (og:image, twitter:image, facebook:image). If OG images are unavailable, use scrape.do API to capture page screenshots. Store images on server with configurable hosting location.
- **Favicon Extraction**: Extract favicons from metadata and store on server with configurable hosting location.
- **Structured Data Storage**: Save all scraped data in organized .md format for LLM consumption. Support multi-page scraping for FAQ, pricing, and feature pages. Store scraped .md files as reference documents for future use.
- **Cost Optimization**: Initially scrape single pages to minimize costs, with option to expand to multi-page scraping.

### 2. AI Integration & Model Selection
- **Dual API Integration**: Implement both OpenAI API and OpenRouter API integration with dynamic model selection
- **Model Options**: Support for Gemini 2.5 Pro and OpenAI GPT-4o with configuration-based switching
- **Advanced Features**: Utilize OpenRouter's prompt caching and other optimization features
- **LLM-Friendly Formatting**: Convert scraped .md data into optimized format for AI processing

### 3. Editorial & Content Management
- **Manual Vetting Feature**: Add admin capability to mark tools as "manually vetted by our editorial team" with custom featured dates (e.g., "first featured on August 7th 2024"). This should NOT be AI-generated content.
- **Content Validation**: Implement system prompts with validation rules and formatting requirements
- **User Prompt Management**: Allow admin editing of user prompts with test validations

### 4. Bulk Processing & Data Import
- **Bulk URL Processing**: UI for loading multiple URLs from:
  - Plain text files (.txt)
  - JSON files with field mapping capabilities
  - Partial data import with AI completion of missing fields
- **Progress Monitoring**: Real-time UI to track bulk processing progress
- **Data Mapping**: Support for importing partial tool data and using AI to generate remaining content

### 5. Advanced Admin Panel Features
- **Job Management Dashboard**: 
  - View job progress, logs, and status
  - Access scraped data and AI-generated content
  - Debug information for every tool processing attempt
  - Pause, resume, and stop job controls
- **Data Inspection**: View all scraped .md files, passed data, and AI responses
- **Error Handling**: Manage partial scrapes, API failures, and processing errors
- **Configuration Management**: Environment-based configuration for all settings

### 6. User Submission Workflows
- **Simple Submission**: Users submit only URL, admin uses scrape-and-generate feature post-approval
- **Full Submission**: Users manually provide all required content fields
- **Approval Workflow**: Admin review and approval process before AI content generation

### 7. Database & System Integration
- **Database Validation**: Review current Supabase schema against required tool data fields
- **Schema Updates**: Modify database structure as needed for new features
- **Data Type Handling**: Ensure proper handling of JSON fields, strings, and other data types
- **API Integration**: Use scrape.do API (key: 8e7e405ff81145c4afe447610ddb9a7f785f494dddc) for web scraping

## Documentation Structure
Split this implementation into smaller, manageable tasks and create separate documentation files for:
1. **System Architecture Document**: Overall system design and data flow
2. **API Integration Guide**: OpenAI and OpenRouter implementation details
3. **Scraping System Documentation**: scrape.do integration and data processing
4. **Admin Panel Specifications**: UI/UX requirements and functionality
5. **Database Schema Updates**: Required changes and migration plan
6. **Bulk Processing Workflow**: Import and processing pipeline design
7. **Error Handling & Recovery**: Failure scenarios and recovery procedures
8. **Configuration Management**: Environment variables and settings
9. **Testing & Validation Plan**: Quality assurance and testing strategy

## Reference Materials
- OpenRouter Documentation: https://openrouter.ai/docs/quickstart
- OpenRouter Prompt Caching: https://openrouter.ai/docs/features/prompt-caching
- Scrape.do Documentation: https://scrape.do/documentation/
- Current database schema and tool data requirements
- Existing chat.txt discussion for context and requirements
- Analyze current `docs/project-tasks.md` for integration points
- Consider existing background job system for migration strategy

This documentation should be finalized before beginning implementation to ensure all requirements are clearly defined and technical approaches are validated.